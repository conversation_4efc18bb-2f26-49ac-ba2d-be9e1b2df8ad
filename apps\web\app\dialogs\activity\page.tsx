"use client";

import { <PERSON><PERSON> } from "@workspace/ui/components/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@workspace/ui/components/dialog";
import {
  Sheet,
  SheetTrigger,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
} from "@workspace/ui/components/sheet";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
  DropdownMenuPortal,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from "@workspace/ui/components/dropdown-menu";
import { Label } from "@workspace/ui/components/label";
import {
  NestedCheckbox,
  NestedCheckboxItem,
} from "@workspace/ui/components/nested-checkbox";
import { Search } from "@workspace/ui/components/search";
import { useRef, useState } from "react";
import { ArrowDownNarrowWide, Filter, LibraryBig } from "lucide-react";

// Activity categories data structure
const activityCategories: NestedCheckboxItem[] = [
  {
    id: "oyun",
    label: "Oyun",
    children: [
      {
        id: "rekabetci",
        label: "Rekabetçi",
        children: [
          { id: "fps", label: "FPS" },
          { id: "moba", label: "MOBA" },
          { id: "battle-royale", label: "Battle Royale" },
        ],
      },
      {
        id: "senaryo",
        label: "Hikaye Odaklı",
      },
      {
        id: "mmo-rpg",
        label: "MMO",
      },
      {
        id: "masa-parti",
        label: "Masa Oyunları",
      },
    ],
  },
  {
    id: "reaksiyon",
    label: "Reaksiyon",
  },
  {
    id: "uretim",
    label: "Üretim",
  },
];

export default function DialogDemo() {
  const container = useRef<HTMLDivElement>(null);
  const [categoriesOpen, setCategoriesOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  return (
    <Dialog open>
      <DialogContent className="sm:max-w-[700px] overflow-hidden">
        <div
          className="absolute pointer-events-none inset-0 overflow-clip rounded-lg"
          ref={container}
        ></div>
        <DialogHeader className="flex-row justify-between gap-4">
          <DialogTitle variant="stripe">{"AKTİVİTE SEÇ"}</DialogTitle>
          <div className="flex gap-2 shrink-0 pl-2 pr-8">
            <Search
              value={searchValue}
              onValueChange={setSearchValue}
              onSearch={(value) => {
                console.log("Searching for:", value);
                // Add your search logic here
              }}
              placeholder="Ara"
              size="small"
              align="end"
              className="w-44"
            />
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="small">
                  <ArrowDownNarrowWide className="size-6 stroke-2" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-44" align="end">
                <DropdownMenuLabel>{"Sırala"}</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>
                    {"Aktivite İsmi"}
                  </DropdownMenuSubTrigger>
                  <DropdownMenuPortal>
                    <DropdownMenuSubContent>
                      <DropdownMenuRadioGroup value="asc">
                        <DropdownMenuRadioItem value="asc">
                          (A-Z)
                        </DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="desc">
                          (Z-A)
                        </DropdownMenuRadioItem>
                      </DropdownMenuRadioGroup>
                    </DropdownMenuSubContent>
                  </DropdownMenuPortal>
                </DropdownMenuSub>
                <DropdownMenuSeparator />
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>
                    {"Senpai Sayısı"}
                  </DropdownMenuSubTrigger>
                  <DropdownMenuPortal>
                    <DropdownMenuSubContent>
                      <DropdownMenuRadioGroup value="asc">
                        <DropdownMenuRadioItem value="asc">
                          {"Azalan"}
                        </DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="desc">
                          {"Artan"}
                        </DropdownMenuRadioItem>
                      </DropdownMenuRadioGroup>
                    </DropdownMenuSubContent>
                  </DropdownMenuPortal>
                </DropdownMenuSub>
              </DropdownMenuContent>
            </DropdownMenu>
            <Button
              onClick={() => setCategoriesOpen((value) => !value)}
              size="small"
            >
              <LibraryBig className="size-6" />
            </Button>
          </div>
        </DialogHeader>
        <div className="h-96"></div>

        <Sheet open={categoriesOpen} onOpenChange={setCategoriesOpen}>
          <SheetContent
            overlay={false}
            container={container.current ?? undefined}
            side="right"
            className="w-[300px] h-[calc(100%-5rem)] top-1/2 -translate-y-1/2 absolute border-none mx-4"
          >
            <div className="flex flex-col justify-between rounded-lg border-2 h-full">
              <SheetHeader>
                <SheetTitle>{"Kategoriler"}</SheetTitle>
              </SheetHeader>
              <div className="flex-1 overflow-auto p-4 scrollbar">
                <NestedCheckbox
                  items={activityCategories}
                  className="space-y-2"
                />
              </div>
              <SheetFooter className="flex-row justify-center">
                <Button size={"small"} className="px-15">
                  {"Temizle"}
                </Button>
                <Search
                  placeholder="Kategori ara"
                  size="small"
                  align="end"
                  side="top"
                  className="w-44"
                />
              </SheetFooter>
            </div>
          </SheetContent>
        </Sheet>
        <DialogFooter></DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
